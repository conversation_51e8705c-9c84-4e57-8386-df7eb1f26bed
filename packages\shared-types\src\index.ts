// Shared types for Elite Locker Streaming Integration

// User and Authentication Types
export interface StreamingUser {
  id: string;
  username: string;
  displayName: string;
  profileImage?: string;
  streamingEnabled: boolean;
  overlayUrl: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface StreamingSettings {
  userId: string;
  theme: OverlayTheme;
  dataSharing: DataSharingSettings;
  overlayPosition: OverlayPosition;
  customColors?: CustomColors;
  showPersonalStats: boolean;
  showGoals: boolean;
  showCurrentExercise: boolean;
  showSessionStats: boolean;
}

// Workout Data Types
export interface WorkoutUpdate {
  sessionId: string;
  userId: string;
  currentExercise: {
    name: string;
    category: string;
    muscleGroups: string[];
  };
  currentSet: {
    setNumber: number;
    reps: number;
    weight: number;
    restTime?: number;
    completed: boolean;
  };
  sessionProgress: {
    exercisesCompleted: number;
    totalExercises: number;
    timeElapsed: number; // in seconds
    estimatedTimeRemaining?: number;
  };
  timestamp: Date;
}

export interface SessionStats {
  sessionId: string;
  userId: string;
  totalTime: number; // in seconds
  exercisesCompleted: number;
  totalSets: number;
  totalReps: number;
  totalVolume: number; // weight * reps
  caloriesBurned?: number;
  averageRestTime?: number;
  personalRecords: PersonalRecord[];
  timestamp: Date;
}

export interface PersonalRecord {
  exerciseName: string;
  type: 'weight' | 'reps' | 'volume' | 'time';
  value: number;
  previousValue?: number;
  improvement: number;
}

// Overlay Configuration Types
export type OverlayTheme = 'default' | 'neon' | 'minimal' | 'gaming';

export interface OverlayPosition {
  x: number; // percentage from left
  y: number; // percentage from top
  width: number; // percentage of screen width
  height: number; // percentage of screen height
}

export interface CustomColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

export interface DataSharingSettings {
  shareCurrentExercise: boolean;
  sharePersonalStats: boolean;
  shareGoals: boolean;
  shareProgressPhotos: boolean;
  shareWorkoutNotes: boolean;
  allowViewerInteraction: boolean;
}

// Socket.io Event Types
export interface ServerToClientEvents {
  workoutUpdate: (data: WorkoutUpdate) => void;
  sessionStats: (data: SessionStats) => void;
  userConnected: (data: { userId: string; username: string }) => void;
  userDisconnected: (data: { userId: string }) => void;
  error: (data: { message: string; code?: string }) => void;
  connectionStatus: (data: { status: 'connected' | 'disconnected' | 'reconnecting' }) => void;
}

export interface ClientToServerEvents {
  joinStream: (data: { overlayUrl: string }) => void;
  leaveStream: (data: { overlayUrl: string }) => void;
  publishWorkoutUpdate: (data: WorkoutUpdate) => void;
  publishSessionStats: (data: SessionStats) => void;
  requestCurrentData: (data: { overlayUrl: string }) => void;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
}

export interface StreamingApiEndpoints {
  '/api/streaming/enable': {
    POST: {
      body: { userId: string };
      response: ApiResponse<{ overlayUrl: string }>;
    };
  };
  '/api/streaming/settings': {
    PUT: {
      body: Partial<StreamingSettings>;
      response: ApiResponse<StreamingSettings>;
    };
    GET: {
      query: { userId: string };
      response: ApiResponse<StreamingSettings>;
    };
  };
  '/api/streaming/overlay/:overlayUrl': {
    GET: {
      response: ApiResponse<{
        user: StreamingUser;
        settings: StreamingSettings;
        currentWorkout?: WorkoutUpdate;
        sessionStats?: SessionStats;
      }>;
    };
  };
}

// Database Models
export interface WorkoutSession {
  id: string;
  userId: string;
  name: string;
  startTime: Date;
  endTime?: Date;
  exercises: Exercise[];
  isStreaming: boolean;
  streamViewers?: number;
  status: 'active' | 'completed' | 'paused';
}

export interface Exercise {
  id: string;
  name: string;
  category: string;
  muscleGroups: string[];
  sets: ExerciseSet[];
  notes?: string;
  restTime?: number;
}

export interface ExerciseSet {
  setNumber: number;
  reps: number;
  weight: number;
  restTime?: number;
  completed: boolean;
  timestamp: Date;
}

// Error Types
export class StreamingError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'StreamingError';
  }
}

// Utility Types
export type StreamingEventType = keyof ServerToClientEvents;
export type StreamingStatus = 'inactive' | 'starting' | 'active' | 'paused' | 'stopping';

// Animation and UI Types
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

export interface OverlayComponent {
  id: string;
  type: 'workout-panel' | 'session-stats' | 'progress-bar' | 'achievement-popup';
  visible: boolean;
  position: OverlayPosition;
  animation?: AnimationConfig;
}

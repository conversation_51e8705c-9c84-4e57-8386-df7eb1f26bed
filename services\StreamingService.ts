import AsyncStorage from '@react-native-async-storage/async-storage';
import { io, Socket } from 'socket.io-client';
// Note: In a real implementation, these types would be imported from the shared-types package
// For now, we'll define them locally to avoid import issues

interface WorkoutUpdate {
  sessionId: string;
  userId: string;
  currentExercise: {
    name: string;
    category: string;
    muscleGroups: string[];
  };
  currentSet: {
    setNumber: number;
    reps: number;
    weight: number;
    restTime?: number;
    completed: boolean;
  };
  sessionProgress: {
    exercisesCompleted: number;
    totalExercises: number;
    timeElapsed: number;
    estimatedTimeRemaining?: number;
  };
  timestamp: Date;
}

interface SessionStats {
  sessionId: string;
  userId: string;
  totalTime: number;
  exercisesCompleted: number;
  totalSets: number;
  totalReps: number;
  totalVolume: number;
  caloriesBurned?: number;
  averageRestTime?: number;
  personalRecords: any[];
  timestamp: Date;
}

interface StreamingSettings {
  userId: string;
  theme: 'default' | 'neon' | 'minimal' | 'gaming';
  dataSharing: {
    shareCurrentExercise: boolean;
    sharePersonalStats: boolean;
    shareGoals: boolean;
    shareProgressPhotos: boolean;
    shareWorkoutNotes: boolean;
    allowViewerInteraction: boolean;
  };
  overlayPosition: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  customColors?: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  showPersonalStats: boolean;
  showGoals: boolean;
  showCurrentExercise: boolean;
  showSessionStats: boolean;
}

type StreamingStatus = 'inactive' | 'starting' | 'active' | 'paused' | 'stopping';

interface ServerToClientEvents {
  workoutUpdate: (data: WorkoutUpdate) => void;
  sessionStats: (data: SessionStats) => void;
  userConnected: (data: { userId: string; username: string }) => void;
  userDisconnected: (data: { userId: string }) => void;
  error: (data: { message: string; code?: string }) => void;
  connectionStatus: (data: { status: 'connected' | 'disconnected' | 'reconnecting' }) => void;
}

interface ClientToServerEvents {
  joinStream: (data: { overlayUrl: string }) => void;
  leaveStream: (data: { overlayUrl: string }) => void;
  publishWorkoutUpdate: (data: WorkoutUpdate) => void;
  publishSessionStats: (data: SessionStats) => void;
  requestCurrentData: (data: { overlayUrl: string }) => void;
}

interface StreamingConfig {
  apiUrl: string;
  socketUrl: string;
  reconnectionAttempts: number;
  reconnectionDelay: number;
}

export class StreamingService {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private isConnected: boolean = false;
  private isStreaming: boolean = false;
  private currentUserId: string | null = null;
  private overlayUrl: string | null = null;
  private config: StreamingConfig;
  private reconnectionAttempts: number = 0;
  private maxReconnectionAttempts: number = 5;
  private listeners: Map<string, Function[]> = new Map();

  constructor(config?: Partial<StreamingConfig>) {
    this.config = {
      apiUrl: 'http://localhost:3001/api',
      socketUrl: 'http://localhost:3001',
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      ...config
    };

    this.maxReconnectionAttempts = this.config.reconnectionAttempts;
  }

  /**
   * Initialize the streaming service
   */
  public async initialize(userId: string): Promise<void> {
    this.currentUserId = userId;

    // Load saved streaming settings
    await this.loadStreamingSettings();

    // Connect to socket if streaming is enabled
    if (this.isStreaming) {
      await this.connect();
    }
  }

  /**
   * Enable streaming for the current user
   */
  public async enableStreaming(): Promise<{ overlayUrl: string }> {
    if (!this.currentUserId) {
      throw new Error('User ID not set. Call initialize() first.');
    }

    try {
      const response = await fetch(`${this.config.apiUrl}/streaming/enable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': await this.getAuthToken()
        },
        body: JSON.stringify({ userId: this.currentUserId })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error?.message || 'Failed to enable streaming');
      }

      this.overlayUrl = data.data.overlayUrl;
      this.isStreaming = true;

      // Save settings
      await this.saveStreamingSettings();

      // Connect to socket
      await this.connect();

      this.emit('streamingEnabled', { overlayUrl: this.overlayUrl });

      return { overlayUrl: this.overlayUrl };
    } catch (error) {
      console.error('Error enabling streaming:', error);
      throw error;
    }
  }

  /**
   * Disable streaming for the current user
   */
  public async disableStreaming(): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('User ID not set. Call initialize() first.');
    }

    try {
      await fetch(`${this.config.apiUrl}/streaming/disable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': await this.getAuthToken()
        },
        body: JSON.stringify({ userId: this.currentUserId })
      });

      this.isStreaming = false;
      this.overlayUrl = null;

      // Disconnect socket
      this.disconnect();

      // Clear saved settings
      await AsyncStorage.removeItem('streaming_settings');

      this.emit('streamingDisabled');
    } catch (error) {
      console.error('Error disabling streaming:', error);
      throw error;
    }
  }

  /**
   * Publish workout update to stream
   */
  public publishWorkoutUpdate(workoutUpdate: WorkoutUpdate): void {
    if (!this.isConnected || !this.socket) {
      console.warn('Not connected to streaming server');
      return;
    }

    if (!this.isStreaming) {
      console.warn('Streaming is not enabled');
      return;
    }

    this.socket.emit('publishWorkoutUpdate', workoutUpdate);
  }

  /**
   * Publish session stats to stream
   */
  public publishSessionStats(sessionStats: SessionStats): void {
    if (!this.isConnected || !this.socket) {
      console.warn('Not connected to streaming server');
      return;
    }

    if (!this.isStreaming) {
      console.warn('Streaming is not enabled');
      return;
    }

    this.socket.emit('publishSessionStats', sessionStats);
  }

  /**
   * Get current streaming status
   */
  public getStreamingStatus(): {
    isStreaming: boolean;
    isConnected: boolean;
    overlayUrl: string | null;
    userId: string | null;
  } {
    return {
      isStreaming: this.isStreaming,
      isConnected: this.isConnected,
      overlayUrl: this.overlayUrl,
      userId: this.currentUserId
    };
  }

  /**
   * Connect to the streaming server
   */
  private async connect(): Promise<void> {
    if (this.socket && this.isConnected) {
      return;
    }

    try {
      this.socket = io(`${this.config.socketUrl}/streaming`, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
        auth: {
          token: await this.getAuthToken()
        }
      });

      this.setupSocketListeners();

      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

    } catch (error) {
      console.error('Error connecting to streaming server:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the streaming server
   */
  private disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.reconnectionAttempts = 0;
  }

  /**
   * Setup socket event listeners
   */
  private setupSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to streaming server');
      this.isConnected = true;
      this.reconnectionAttempts = 0;
      this.emit('connected');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from streaming server:', reason);
      this.isConnected = false;
      this.emit('disconnected', { reason });
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.isConnected = false;
      this.emit('connectionError', { error: error.message });
    });

    this.socket.on('error', (error) => {
      console.error('Streaming error:', error);
      this.emit('error', error);
    });

    this.socket.on('connectionStatus', (data) => {
      this.emit('connectionStatus', data);
    });
  }

  /**
   * Load streaming settings from storage
   */
  private async loadStreamingSettings(): Promise<void> {
    try {
      const settings = await AsyncStorage.getItem('streaming_settings');
      if (settings) {
        const parsed = JSON.parse(settings);
        this.isStreaming = parsed.isStreaming || false;
        this.overlayUrl = parsed.overlayUrl || null;
      }
    } catch (error) {
      console.error('Error loading streaming settings:', error);
    }
  }

  /**
   * Save streaming settings to storage
   */
  private async saveStreamingSettings(): Promise<void> {
    try {
      const settings = {
        isStreaming: this.isStreaming,
        overlayUrl: this.overlayUrl,
        userId: this.currentUserId
      };
      await AsyncStorage.setItem('streaming_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving streaming settings:', error);
    }
  }

  /**
   * Get authentication token
   */
  private async getAuthToken(): Promise<string> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      return token ? `Bearer ${token}` : '';
    } catch (error) {
      console.error('Error getting auth token:', error);
      return '';
    }
  }

  /**
   * Event listener management
   */
  public on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  public off(event: string, callback?: Function): void {
    if (!this.listeners.has(event)) return;

    if (callback) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(event);
    }
  }

  private emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    this.disconnect();
    this.listeners.clear();
  }
}

// Export singleton instance
export const streamingService = new StreamingService();

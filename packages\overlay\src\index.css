/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility classes */
.transparent-bg {
  background: transparent !important;
}

.glass-effect {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.neon-glow {
  box-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.counter-animation {
  animation: counterPop 0.3s ease-out;
}

@keyframes counterPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Theme-specific styles */
.theme-default {
  --primary-color: #4CAF50;
  --secondary-color: #2E7D32;
  --accent-color: #66BB6A;
  --background-color: rgba(0, 0, 0, 0.8);
  --text-color: #FFFFFF;
  --text-secondary: #CCCCCC;
}

.theme-neon {
  --primary-color: #FF0080;
  --secondary-color: #8000FF;
  --accent-color: #00FFFF;
  --background-color: rgba(0, 0, 0, 0.9);
  --text-color: #FFFFFF;
  --text-secondary: #FF00FF;
}

.theme-minimal {
  --primary-color: #FFFFFF;
  --secondary-color: #F5F5F5;
  --accent-color: #E0E0E0;
  --background-color: rgba(255, 255, 255, 0.9);
  --text-color: #000000;
  --text-secondary: #666666;
}

.theme-gaming {
  --primary-color: #00FF00;
  --secondary-color: #FF4500;
  --accent-color: #FFD700;
  --background-color: rgba(0, 0, 0, 0.85);
  --text-color: #FFFFFF;
  --text-secondary: #00FF00;
}

/* Responsive design */
@media (max-width: 768px) {
  .overlay-container {
    padding: 10px;
  }
  
  .workout-panel {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .overlay-container {
    padding: 5px;
  }
  
  .workout-panel {
    font-size: 12px;
  }
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #FFFFFF;
  }
}

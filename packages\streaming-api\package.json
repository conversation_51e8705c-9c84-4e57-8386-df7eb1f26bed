{"name": "@elite-locker/streaming-api", "version": "1.0.0", "description": "Backend API server for Elite Locker streaming integration", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "clean": "rm -rf dist", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "mongoose": "^8.1.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "joi": "^17.11.0", "winston": "^3.11.0", "@elite-locker/shared-types": "file:../shared-types"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.6", "typescript": "^5.8.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "engines": {"node": ">=18.0.0"}}
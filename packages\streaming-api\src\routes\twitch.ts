import { Router } from 'express';
import { TwitchService } from '../services/TwitchService';
import { TwitchChatBot } from '../services/TwitchChatBot';
import { authMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';
import { ApiResponse, StreamingError } from '@elite-locker/shared-types';
import { v4 as uuidv4 } from 'uuid';

const router = Router();
const twitchService = new TwitchService();
const chatBots = new Map<string, TwitchChatBot>(); // userId -> chatBot

/**
 * GET /api/twitch/auth-url
 * Get Twitch OAuth authorization URL
 */
router.get('/auth-url', authMiddleware, async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'User ID is required',
          code: 'MISSING_USER_ID'
        }
      } as ApiResponse);
    }

    const state = uuidv4(); // Use this to verify the callback
    // TODO: Store state in database/cache with userId for verification
    
    const authUrl = twitchService.getAuthorizationUrl(state);
    
    res.status(200).json({
      success: true,
      data: {
        authUrl,
        state
      }
    } as ApiResponse);
    
  } catch (error) {
    logger.error('Error generating Twitch auth URL:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * POST /api/twitch/callback
 * Handle Twitch OAuth callback
 */
router.post('/callback', authMiddleware, async (req, res) => {
  try {
    const { code, state, userId } = req.body;
    
    if (!code || !state || !userId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Code, state, and user ID are required',
          code: 'MISSING_PARAMETERS'
        }
      } as ApiResponse);
    }

    // TODO: Verify state matches what was stored for this user
    
    // Exchange code for tokens
    const tokens = await twitchService.exchangeCodeForTokens(code);
    
    // Get user information
    const twitchUser = await twitchService.getUser(tokens.accessToken);
    
    // TODO: Store tokens and user info in database
    // For now, we'll return them to the client
    
    res.status(200).json({
      success: true,
      data: {
        tokens,
        twitchUser,
        message: 'Successfully connected to Twitch!'
      }
    } as ApiResponse);
    
    logger.info(`User ${userId} successfully connected Twitch account: ${twitchUser.login}`);
    
  } catch (error) {
    logger.error('Error handling Twitch callback:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * POST /api/twitch/disconnect
 * Disconnect Twitch integration
 */
router.post('/disconnect', authMiddleware, async (req, res) => {
  try {
    const { userId, accessToken } = req.body;
    
    if (!userId || !accessToken) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'User ID and access token are required',
          code: 'MISSING_PARAMETERS'
        }
      } as ApiResponse);
    }

    // Disconnect chat bot if running
    const chatBot = chatBots.get(userId);
    if (chatBot) {
      await chatBot.disconnect();
      chatBots.delete(userId);
    }

    // Revoke Twitch token
    await twitchService.revokeToken(accessToken);
    
    // TODO: Remove tokens and settings from database
    
    res.status(200).json({
      success: true,
      data: {
        message: 'Successfully disconnected from Twitch'
      }
    } as ApiResponse);
    
    logger.info(`User ${userId} disconnected Twitch integration`);
    
  } catch (error) {
    logger.error('Error disconnecting Twitch:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * GET /api/twitch/stream-status
 * Get current stream status
 */
router.get('/stream-status', authMiddleware, async (req, res) => {
  try {
    const { twitchUserId } = req.query;
    
    if (!twitchUserId || typeof twitchUserId !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Twitch user ID is required',
          code: 'MISSING_TWITCH_USER_ID'
        }
      } as ApiResponse);
    }

    const stream = await twitchService.getStream(twitchUserId);
    
    res.status(200).json({
      success: true,
      data: {
        isLive: !!stream,
        stream
      }
    } as ApiResponse);
    
  } catch (error) {
    logger.error('Error getting stream status:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * POST /api/twitch/update-stream
 * Update stream title and category
 */
router.post('/update-stream', authMiddleware, async (req, res) => {
  try {
    const { accessToken, twitchUserId, title, categoryId } = req.body;
    
    if (!accessToken || !twitchUserId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Access token and Twitch user ID are required',
          code: 'MISSING_PARAMETERS'
        }
      } as ApiResponse);
    }

    await twitchService.updateStream(accessToken, twitchUserId, title, categoryId);
    
    res.status(200).json({
      success: true,
      data: {
        message: 'Stream updated successfully'
      }
    } as ApiResponse);
    
    logger.info(`Updated stream for user ${twitchUserId}`, { title, categoryId });
    
  } catch (error) {
    logger.error('Error updating stream:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * POST /api/twitch/chat-bot/start
 * Start the chat bot for a user
 */
router.post('/chat-bot/start', authMiddleware, async (req, res) => {
  try {
    const { userId, accessToken, twitchUsername, channels } = req.body;
    
    if (!userId || !accessToken || !twitchUsername || !channels) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'User ID, access token, username, and channels are required',
          code: 'MISSING_PARAMETERS'
        }
      } as ApiResponse);
    }

    // Stop existing chat bot if running
    const existingBot = chatBots.get(userId);
    if (existingBot) {
      await existingBot.disconnect();
    }

    // Create and start new chat bot
    const chatBot = new TwitchChatBot();
    await chatBot.connect({
      username: twitchUsername,
      accessToken,
      channels: Array.isArray(channels) ? channels : [channels]
    });

    chatBots.set(userId, chatBot);
    
    res.status(200).json({
      success: true,
      data: {
        message: 'Chat bot started successfully',
        status: chatBot.getStatus()
      }
    } as ApiResponse);
    
    logger.info(`Started chat bot for user ${userId} on channels: ${channels}`);
    
  } catch (error) {
    logger.error('Error starting chat bot:', error);
    
    if (error instanceof StreamingError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          message: error.message,
          code: error.code
        }
      } as ApiResponse);
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * POST /api/twitch/chat-bot/stop
 * Stop the chat bot for a user
 */
router.post('/chat-bot/stop', authMiddleware, async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'User ID is required',
          code: 'MISSING_USER_ID'
        }
      } as ApiResponse);
    }

    const chatBot = chatBots.get(userId);
    if (!chatBot) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Chat bot not found or not running',
          code: 'CHAT_BOT_NOT_FOUND'
        }
      } as ApiResponse);
    }

    await chatBot.disconnect();
    chatBots.delete(userId);
    
    res.status(200).json({
      success: true,
      data: {
        message: 'Chat bot stopped successfully'
      }
    } as ApiResponse);
    
    logger.info(`Stopped chat bot for user ${userId}`);
    
  } catch (error) {
    logger.error('Error stopping chat bot:', error);
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

/**
 * GET /api/twitch/chat-bot/status
 * Get chat bot status
 */
router.get('/chat-bot/status', authMiddleware, async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'User ID is required',
          code: 'MISSING_USER_ID'
        }
      } as ApiResponse);
    }

    const chatBot = chatBots.get(userId);
    const status = chatBot ? chatBot.getStatus() : {
      isConnected: false,
      channels: [],
      commandCount: 0,
      activeChallenges: 0
    };
    
    res.status(200).json({
      success: true,
      data: status
    } as ApiResponse);
    
  } catch (error) {
    logger.error('Error getting chat bot status:', error);
    
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      }
    } as ApiResponse);
  }
});

// Export the chat bots map for use in other services
export { chatBots };
export { router as twitchRoutes };
